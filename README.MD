# Logos Mind - Modern Christian Blog

This repository contains the code for the Logos Mind blog, a simple static website built with HTML, CSS, and vanilla JavaScript.

## Features

*   **Clean, modern design:** Uses holographic-inspired elements and light/dark theme potential.
*   **Dynamic Content Loading:** 
    *   Recent posts on the index page are loaded from `media/index-recent.json`.
    *   All articles on the articles page are loaded from `media/all-articles.json`.
    *   The scripture panel on the index page loads verses from `media/verse-of-the-day.json` and cycles through them daily based on the user's system date.
*   **Pagination:** The main articles page (`articles.html`) uses client-side pagination (30 articles per page) when not filtering via search.
*   **Global Search Suggestions:** A search bar in the header provides live suggestions (title + description) on *all* pages based on content in `media/all-articles.json`. Clicking a suggestion navigates to the article.
*   **Article Page Filtering:** Clicking the search button *only* filters the main article grid when viewing the `articles.html` page.
*   **Featured Card Hover Effect (Index Page):** The main featured card on `index.html` uses a CSS mask effect to reveal a background image (`images/featured-background.jpg`) around the user's cursor on hover. The title and description text fade out during the hover. This picture is 1200x300px
*   **Animated Background:** Subtle particle animations and gradient background effects.
*   **Responsive Design:** Basic responsiveness for smaller screens.

## Project Structure

```
/
├── index.html              # Main landing page (with featured card, recent posts, scripture)
├── about.html              # About page
├── articles.html           # Page displaying all articles with pagination & search filtering
├── privacy-policy.html     # Privacy Policy page
├── styles.css              # Main stylesheet (light theme)
├── script.js               # Main JavaScript (content loading, search, animations, etc.)
├── media/                  # Folder for data files
│   ├── index-recent.json   # Data for recent posts on index.html
│   ├── all-articles.json   # Data for ALL articles (powers search & articles page)
│   └── verse-of-the-day.json # Data for daily scripture rotation
├── images/                 # Folder for site images
│   ├── featured-background.jpg # Background for index card hover. Source Recommend: ~1800x900px for quality. Display size: max 1200px width, height varies by content.
│   └── finding-peace.jpg   # Example article banner/thumbnail
│   └── placeholder-300x150.png # Local placeholder for article thumbnails
├── april-2025/             # Example folder for organizing articles by date
│   ├── finding-peace-in-daily-chaos-03-04-2025.html # Example article page
│   └── the-art-of-prayerful-living-09-04-25.html    # Example article page
├── Article_creation_tool/  # Tool for generating article HTML and JSON
│   ├── base.html           # The HTML interface for the tool
│   └── script_tool.js      # JavaScript logic for the article creation tool
└── README.MD               # This file
```
*Note: Headers contain standard search elements (`#globalSearchInput`, `#globalSearchButton`, `#globalSuggestionsDropdown`) used by `script.js`.* 

## Adding New Articles (Manual Method)

This section describes the original manual method. For a faster approach, see the "Article Creation Tool" section below.

1.  **Create the HTML File:**
    *   Copy an existing article's HTML file (e.g., `april-2025/finding-peace-in-daily-chaos-03-04-2025.html`) or create a new one.
    *   Save it in an appropriate folder (e.g., create a new month/year folder or place it in the root).
    *   Update the `<title>`, main `<h1>`, article content (paragraphs, headings, etc.), and publish date (`<p class="post-meta">`).
    *   **Crucially:** Update the `<img class="article-hero-image" src="...">` path to the new article's main image.
    *   **Verify Paths:** Ensure the paths in `<link rel="stylesheet" href="...">`, `<script src="...">`, and all navigation links (`<nav> <a href="...">`, `<h1> <a href="...">`) are correct relative to the *new file's location*.
    *   **Include Stylesheets:** Make sure both `styles.css` and `dark-theme.css` are linked in the `<head>`:
        ```html
        <link rel="stylesheet" href="../styles.css"> <!-- Adjust path as needed -->
        <link rel="stylesheet" href="../dark-theme.css"> <!-- Adjust path as needed -->
        ```
    *   **Add SEO Tags to `<head>` (Crucial for External SEO):** 
        *   Add a unique and compelling `<meta name="description" content="...">` summarizing the article (approx. 150-160 chars).
        *   (Optional) Add relevant `<meta name="keywords" content="...">`.
        *   Add the JSON-LD Schema Markup `<script type="application/ld+json">...</script>`. Copy the structure from an existing article and **carefully update** the `headline`, `image` URL, `datePublished`, `dateModified`, `description`, and `mainEntityOfPage.@id` (the full URL of the new article) to match the new article's details. Ensure the `publisher.logo.url` is correct.

2.  **Add Image:**
    *   Place the article's banner/thumbnail image in the `/images` folder.
    *   **Article Image Dimensions Recommendation:**
        *   Since the same image is used for the large article banner (`.article-hero-image`) and the smaller post-card thumbnail, specific dimensions are recommended for best results.
        *   **Source Image Size:** Aim for approximately **1800px wide by 500-600px high**.
        *   **Composition:** Keep the **main subject of the image horizontally centered** so it remains visible in the thumbnail (which crops the sides).
        *   **Optimization:** Always optimize images for the web.

3.  **Update JSON Data (`media/all-articles.json`) (Crucial for Internal Search & Articles Page):**
    *   Open `media/all-articles.json`.
    *   **Order Matters:** Add the new article object to the **beginning** of the JSON array if you want it to appear first when sorted by "Newest" on the Articles page (the default sort relies on this file's order).
    *   Add a new JSON object to the array for your article. This powers the **internal site search** (suggestions/filtering) and the list on `articles.html`.
    *   Include at least:
        ```json
        {
          "title": "Your New Article Title", // For display & search
          "excerpt": "A short summary used for internal search.",
          "link": "path/to/your/new-article.html", // Relative to root
          "imageUrl": "images/your-new-image.jpg", // Relative to root
          "tags": ["Faith", "Prayer"], // Add up to 2 relevant tags to display on the post card
          "metaDescription": "Optional: Used for the search suggestion dropdown text."
        }
        ```
    *   **Tags:** The `tags` array is used to display the first two tags on the article's post card. Include relevant keywords here.
    *   **Important:** Ensure `link` and `imageUrl` paths are relative to the *root* directory. Remember JSON syntax (commas between objects).
    *   **Note:** Adding/updating this file is the **only** step needed to make the article appear in search suggestions and the main `articles.html` list/filter.

4.  **(Optional) Update Recent Posts (`media/index-recent.json`):**
    *   If this article should appear on the `index.html` page's recent posts section, open `media/index-recent.json`.
    *   Add the *same* JSON object structure for the article here (or replace an older one).

## Article Creation Tool

To speed up the process of adding new articles and ensure consistency in HTML structure and JSON entries, an Article Creation Tool is available.

**Location:** `Article_creation_tool/base.html`

**How to Use:**

1.  **Configure the Tool (One-time Setup or when your domain changes):**
    *   Open `Article_creation_tool/script_tool.js` in a text editor.
    *   Near the top, update the following placeholder values:
        *   `websiteBaseUrl`: Change `"https://www.yourdomain.com"` to your actual website's domain (e.g., `"https://www.yourblog.com"`). For local testing, you might use a `file:///` path, but ensure it's your live domain for the final version.
        *   `publisherLogoUrl`: Ensure the path to your logo (e.g., `${websiteBaseUrl}/images/your-logo.png`) is correct. Update if your logo has a different name or path.
        *   `contactEmail`: Change `"<EMAIL>"` to your actual contact email address.

2.  **Open the Tool:**
    *   Navigate to and open `Article_creation_tool/base.html` in your web browser.

3.  **Fill in the Form:**
    *   **Article Title:** The main title of your article.
    *   **Publish Date:** Select the date of publication.
    *   **Article Folder:** If the article HTML file will reside in a subfolder (e.g., `yyyy-mm`), enter the folder name here (e.g., `april-2025`). Leave blank if the article will be in the root directory.
    *   **Article Filename:** The desired filename for the HTML file (e.g., `my-awesome-post.html`). Use URL-friendly characters.
    *   **Author Name:** Defaults to "Logos Mind" but can be overridden.
    *   **Article Content:** Write or paste your article content here. Basic HTML tags (`<p>`, `<h2>`, `<ul>`, `<li>`, `<strong>`, `<em>`, etc.) are allowed and will be preserved.
    *   **Main Image Filename:** Just the filename of the image (e.g., `article-banner.jpg`). This image must be manually placed in your website's main `/images/` folder.
    *   **Meta Description:** A concise summary for SEO (around 150-160 characters).
    *   **Meta Keywords:** Comma-separated keywords relevant to your article.
    *   **Tags:** Comma-separated tags for display on article cards (the first two are typically shown).
    *   **Add to Recent Posts:** Check this box if you want the article to be included in the data for `media/index-recent.json`.

4.  **Generate Files:**
    *   Click the "Generate Article Files" button.
    *   The tool will populate three text areas with:
        *   The complete HTML for your new article.
        *   The JSON object for `media/all-articles.json`.
        *   The JSON object for `media/index-recent.json` (if the box was checked).

5.  **Save and Update Your Website Manually:**
    *   **HTML File:**
        *   Copy the entire content from the "Generated HTML File Content" textarea.
        *   Create a new `.html` file in the appropriate directory (e.g., `april-2025/my-awesome-post.html` or directly in the root if no folder was specified).
        *   Paste the copied HTML into this new file and save it.
    *   **`media/all-articles.json`:**
        *   Copy the content from the "Generated JSON for `media/all-articles.json`" textarea.
        *   Open your website's `media/all-articles.json` file.
        *   **Crucially, paste this new JSON object at the BEGINNING of the existing JSON array** (i.e., after the opening `[` and before the first existing `{`, ensuring there's a comma after your newly pasted object if other objects follow).
    *   **`media/index-recent.json` (If Applicable):**
        *   If you checked "Add to Recent Posts," copy the content from the "Generated JSON for `media/index-recent.json`" textarea.
        *   Open your `media/index-recent.json` file and add this new object to the array (or replace an existing one, depending on how many recent posts you display).
    *   **Upload Image:** Don't forget to manually upload the actual image file (specified in "Main Image Filename") to your website's `/images/` directory.

**Sitemap XML Generator (Part of the Tool):**

The Article Creation Tool page (`Article_creation_tool/base.html`) also includes a section to help you generate your `sitemap.xml`.

1.  **Prepare your Data:** Ensure your `media/all-articles.json` file is up-to-date with all your articles.
2.  **Open the Tool:** Navigate to `Article_creation_tool/base.html` in your browser.
3.  **Paste JSON:** Scroll to the "Sitemap XML Generator" section. Copy the *entire content* (the full JSON array) from your `media/all-articles.json` file and paste it into the designated textarea.
4.  **Generate XML:** Click the "Generate Sitemap XML" button.
5.  **Save Sitemap:**
    *   Copy the entire content from the "Generated `sitemap.xml` Content" textarea.
    *   Create or open the `sitemap.xml` file in the **root directory** of your website.
    *   Paste the copied XML into this file, replacing its previous content, and save it.
6.  **Submit to Search Engines:** Submit your `sitemap.xml` (e.g., `https://www.yourdomain.com/sitemap.xml`) to Google Search Console and other webmaster tools.

## Development

No build process is required. Simply open the `.html` files in a web browser.