<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Article Creation Tool | Logos Mind</title>
    <style>
        body { font-family: sans-serif; margin: 20px; background-color: #2c2c2c; color: #e0e0e0; }
        .container { max-width: 900px; margin: auto; background: #3a3a3a; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.4); }
        h1, h2 { color: #e0e0e0; text-align: center; }
        label { display: block; margin-top: 15px; margin-bottom: 5px; font-weight: bold; color: #f0f0f0; }
        input[type="text"], input[type="date"], textarea, select {
            width: calc(100% - 0px); padding: 10px; margin-bottom: 10px; 
            border: 1px solid #555; border-radius: 4px; box-sizing: border-box; 
            background-color: #444; color: #e0e0e0;
        }
        textarea {
            min-height: 150px; 
            white-space: pre-wrap; /* Allow text wrapping */
            overflow-wrap: break-word; /* Ensure long words break */
            resize: vertical; /* Only allow vertical resizing */
        }
        .output-area textarea { min-height: 200px; background-color: #222; font-family: monospace; }
        input[type="text"]::placeholder, textarea::placeholder { color: #999; }
        button { background-color: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; margin-right: 10px; }
        button:hover { background-color: #0056b3; }
        button.clear { background-color: #c82333; }
        button.clear:hover { background-color: #a01c28; }
        .form-section { margin-bottom: 20px; padding: 15px; border: 1px solid #444; border-radius: 5px; background-color: #333;}
        .output-section { margin-top: 30px; margin-bottom: 20px;}
        .output-section textarea { margin-bottom: 5px; }
        .checkbox-label { display: inline-block; margin-left: 5px; font-weight: normal; color: #e0e0e0; }
        hr { margin-top: 30px; margin-bottom: 30px; border-color: #444; }
        .toolbar-button { background-color: #555; color: #e0e0e0; border: 1px solid #666; padding: 5px 10px; margin-right: 5px; cursor: pointer; border-radius: 3px; }
        .toolbar-button:hover { background-color: #666; }
        code { 
            background-color: #454545; 
            padding: 2px 4px; 
            border-radius: 3px; 
            color: #F08080; /* Changed from lightcoral to hex */ 
        }
        .copy-button { background-color: #6c757d; margin-top: 5px; }
        .copy-button:hover { background-color: #5a6268; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Logos Mind Article Creation Tool</h1>

        <form id="articleForm">
            <div class="form-section">
                <h2>Basic Article Information</h2>
                <label for="articleTitle">Article Title:</label>
                <input type="text" id="articleTitle" name="articleTitle" required>
                <small>Character count: <span id="titleCharCount">0</span></small><br>

                <label for="publishDate">Publish Date:</label>
                <input type="date" id="publishDate" name="publishDate" required>
                
                <label for="articleFolder">Article Folder (e.g., april-2025 or root if none):</label>
                <input type="text" id="articleFolder" name="articleFolder" placeholder="e.g., yyyy-mm or leave blank for root">

                <label for="articleFilename">Article Filename (e.g., my-new-article.html):</label>
                <input type="text" id="articleFilename" name="articleFilename" required placeholder="url-friendly-name.html">

                <label for="authorName">Author Name (Defaults to "Logos Mind"):</label>
                <input type="text" id="authorName" name="authorName" placeholder="Logos Mind">
            </div>

            <div class="form-section">
                <h2>Article Content & Media</h2>

                <label for="articleContent">Article Content (HTML allowed):</label>
                <div id="toolbar" style="margin-bottom: 5px; padding: 5px; border: 1px solid #ddd; border-radius: 4px;">
                    <button type="button" class="toolbar-button" data-tag="h1">H1</button>
                    <button type="button" class="toolbar-button" data-tag="h2">H2</button>
                    <button type="button" class="toolbar-button" data-tag="p">P</button>
                    <button type="button" class="toolbar-button" data-tag="strong">Bold</button>
                    <button type="button" class="toolbar-button" data-tag="em">Italic</button>
                    <button type="button" class="toolbar-button" data-tag="a">Link</button>
                    <button type="button" class="toolbar-button" data-tag="img">Image</button>
                    <button type="button" class="toolbar-button" data-tag="ul">List (UL)</button>
                    <button type="button" class="toolbar-button" data-tag="ol">List (OL)</button>
                    <button type="button" class="toolbar-button" data-tag="blockquote">Quote</button>
                    <button type="button" class="toolbar-button" data-tag="figure">Figure</button>
                </div>
                <textarea id="articleContent" name="articleContent" placeholder="Write your article content here. You can use HTML tags like <p>, <h2>, <ul>, etc."></textarea>

                <label for="imageUrl">Main Image Filename (must be in /images/ folder, e.g., my-image.jpg):</label>
                <input type="text" id="imageUrl" name="imageUrl" required placeholder="your-article-image.jpg">
            </div>

            <div class="form-section">
                <h2>SEO & Metadata</h2>
                <label for="metaDescription">Meta Description (for SEO - approx. 150-160 chars):</label>
                <input type="text" id="metaDescription" name="metaDescription" maxlength="160">
                <small>Character count: <span id="metaDescCharCount">0</span> / 160</small><br>

                <label for="metaKeywords">Meta Keywords (comma-separated):</label>
                <input type="text" id="metaKeywords" name="metaKeywords">

                <label for="tags">Tags (comma-separated, for article card display - first 2 shown):</label>
                <input type="text" id="tags" name="tags" placeholder="Faith, Prayer, Hope">
            </div>
            
            <div class="form-section">
                <h2>JSON Data Options</h2>
                <input type="checkbox" id="addToRecent" name="addToRecent" checked>
                <label for="addToRecent" class="checkbox-label">Add to Recent Posts on Homepage (index-recent.json)?</label>
            </div>

            <button type="button" id="generateButton">Generate Article Files</button>
            <button type="button" id="clearFormButton" class="clear">Clear Form</button>
        </form>

        <hr>

        <div class="output-section">
            <h2>Generated HTML File Content</h2>
            <p>Copy the code below and save it as a new .html file (e.g., in <code>[articleFolder]/[articleFilename]</code>).</p>
            <textarea id="generatedHtml" readonly placeholder="Generated HTML will appear here..."></textarea>
            <button type="button" class="copy-button" data-target="generatedHtml">Copy HTML</button>

            <h2>Generated JSON for <code>media/all-articles.json</code></h2>
            <p>Copy this JSON object and add it to the BEGINNING of the array in <code>media/all-articles.json</code>.</p>
            <textarea id="generatedJsonAll" readonly placeholder="Generated JSON for all-articles.json will appear here..."></textarea>
            <button type="button" class="copy-button" data-target="generatedJsonAll">Copy All Articles JSON</button>

            <h2>Generated JSON for <code>media/index-recent.json</code></h2>
            <p>If "Add to Recent Posts" was checked, copy this JSON object and add/replace it in <code>media/index-recent.json</code>.</p>
            <textarea id="generatedJsonRecent" readonly placeholder="Generated JSON for index-recent.json will appear here (if applicable)..."></textarea>
            <button type="button" class="copy-button" data-target="generatedJsonRecent">Copy Recent JSON</button>
        </div>

        <hr>

        <div class="output-section">
            <h2>Sitemap XML Generator</h2>
            <p>This tool helps generate a basic <code>sitemap.xml</code>. For it to include all your articles, you need to provide the content of your <code>media/all-articles.json</code> file.</p>
            
            <div class="form-section">
                <label for="allArticlesJsonInput">Paste content of <code>media/all-articles.json</code> here:</label>
                <textarea id="allArticlesJsonInput" placeholder="Paste the entire JSON array from media/all-articles.json here..."></textarea>
            </div>

            <button type="button" id="generateSitemapButton">Generate Sitemap XML</button>
            
            <label for="generatedSitemapXml" style="margin-top: 20px;">Generated <code>sitemap.xml</code> Content:</label>
            <p>Copy the code below and save it as <code>sitemap.xml</code> in the root directory of your website.</p>
            <textarea id="generatedSitemapXml" readonly placeholder="Generated sitemap.xml will appear here..."></textarea>
            <button type="button" class="copy-button" data-target="generatedSitemapXml">Copy Sitemap XML</button>
        </div>

    </div>

    <script src="script_tool.js"></script>
</body>
</html>
