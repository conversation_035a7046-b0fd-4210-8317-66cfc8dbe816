document.addEventListener('DOMContentLoaded', () => {
    const articleForm = document.getElementById('articleForm');
    const generateButton = document.getElementById('generateButton');
    const clearFormButton = document.getElementById('clearFormButton');

    const generatedHtmlOutput = document.getElementById('generatedHtml');
    const generatedJsonAllOutput = document.getElementById('generatedJsonAll');
    const generatedJsonRecentOutput = document.getElementById('generatedJsonRecent');

    // Sitemap Generator Elements
    const allArticlesJsonInput = document.getElementById('allArticlesJsonInput');
    const generateSitemapButton = document.getElementById('generateSitemapButton');
    const generatedSitemapXmlOutput = document.getElementById('generatedSitemapXml');

    // Character counter elements
    const articleTitleInput = document.getElementById('articleTitle');
    const titleCharCountSpan = document.getElementById('titleCharCount');
    const metaDescriptionInput = document.getElementById('metaDescription');
    const metaDescCharCountSpan = document.getElementById('metaDescCharCount');
    const articleFilenameInput = document.getElementById('articleFilename');
    const publishDateInput = document.getElementById('publishDate');

    // Toolbar elements
    const toolbar = document.getElementById('toolbar');
    const articleContentTextarea = document.getElementById('articleContent');

    // --- DEFAULT VALUES --- (You can change these)
    const defaultAuthor = "Logos Mind";
    const websiteBaseUrl = "https://www.yourdomain.com"; // IMPORTANT: Replace with your actual domain for canonical URLs
    const publisherLogoUrl = `${websiteBaseUrl}/images/logos-mind-logo.png`; // IMPORTANT: Ensure this logo exists
    const contactEmail = "<EMAIL>"; // IMPORTANT: Replace with your actual contact email

    generateButton.addEventListener('click', () => {
        // --- Get Form Values ---
        const articleTitle = document.getElementById('articleTitle').value.trim();
        let publishDateInput = document.getElementById('publishDate').value;
        const articleFolder = document.getElementById('articleFolder').value.trim();
        const articleFilename = document.getElementById('articleFilename').value.trim();
        const authorName = document.getElementById('authorName').value.trim() || defaultAuthor;
        const articleContent = document.getElementById('articleContent').value; // Keep raw HTML content
        const imageUrl = document.getElementById('imageUrl').value.trim();
        const metaDescription = document.getElementById('metaDescription').value.trim();
        const metaKeywords = document.getElementById('metaKeywords').value.trim();
        const tagsInput = document.getElementById('tags').value.trim();
        const addToRecent = document.getElementById('addToRecent').checked;

        // --- Validate required fields ---
        if (!articleTitle || !publishDateInput || !articleFilename || !imageUrl || !articleContent) {
            alert("Please fill in all required fields: Title, Publish Date, Filename, Image URL, and Content.");
            return;
        }
        
        // --- Prepare date formats ---
        const dateObj = new Date(publishDateInput + 'T00:00:00'); // Keep for local formatting
        const formattedPublishDate = dateObj.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
        const isoPublishDate = publishDateInput; // Use the input value directly (already YYYY-MM-DD)

        // --- Construct paths ---
        // Calculate dynamic pathPrefix based on folder depth
        let pathPrefix = './';
        if (articleFolder) {
            // Remove leading/trailing slashes and split by slash
            const folderParts = articleFolder.replace(/^\/+|\/+$/g, '').split('/').filter(part => part !== '');
            const depth = folderParts.length;
            if (depth > 0) {
                pathPrefix = '../'.repeat(depth);
            }
        }
        
        const articlePath = articleFolder ? `${articleFolder.replace(/\/+$/, '')}/${articleFilename}` : articleFilename; // Ensure no trailing slash before filename
        const canonicalUrl = `${websiteBaseUrl}/${articlePath}`.replace(/(?<!:)\/\//g, '/'); 
        const fullImageUrl = `${websiteBaseUrl}/images/${imageUrl}`.replace(/(?<!:)\/\//g, '/');
        const rootRelativeImageUrl = `images/${imageUrl}`;
        const rootRelativeArticlePath = articlePath;

        // --- Prepare Tags ---
        const tagsArray = tagsInput ? tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

        // --- Generate HTML --- 
        const articleHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${articleTitle} | Logos Mind</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="${metaDescription}">
    <meta name="keywords" content="${metaKeywords}">
    
    <!-- Schema.org Markup -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": "${articleTitle}",
      "image": [
        "${fullImageUrl}"
       ],
      "datePublished": "${isoPublishDate}", 
      "dateModified": "${isoPublishDate}", 
      "author": {
        "@type": "Organization", 
        "name": "${authorName}"
      },
       "publisher": {
         "@type": "Organization",
         "name": "Logos Mind",
         "logo": {
           "@type": "ImageObject",
           "url": "${publisherLogoUrl}"
         }
       },
      "description": "${metaDescription}",
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "${canonicalUrl}"
      }
    }
    <\/script>

    <link rel="stylesheet" href="${pathPrefix}styles.css">
    <link rel="stylesheet" href="${pathPrefix}dark-theme.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="light-waves"></div>
    
    <div class="container">
        <header class="holo-header">
            <h1><a href="${pathPrefix}index.html" style="text-decoration: none; color: var(--text);"><span class="holo-gradient">Logos</span>Mind</a></h1>

            <div class="search-container">
                <input type="search" placeholder="Search..." id="globalSearchInput" autocomplete="off">
                <button id="globalSearchButton"><i class="fas fa-search"></i></button>
                <div id="globalSuggestionsDropdown" class="suggestions-dropdown"></div>
            </div>

            <nav>
                <a href="${pathPrefix}index.html" class="holo-button"><i class="fas fa-home"></i> Home</a>
                <a href="${pathPrefix}about.html" class="holo-button"><i class="fas fa-info-circle"></i> About</a>
                <a href="${pathPrefix}articles.html" class="holo-button"><i class="fas fa-newspaper"></i> Articles</a>
            </nav>

            <button id="themeToggle" class="theme-toggle-button" aria-label="Toggle theme">
                 <i class="fas fa-sun"></i> 
            </button>
        </header>

        <img src="${pathPrefix}${rootRelativeImageUrl}" alt="${articleTitle} Banner" class="article-hero-image">

        <section class="holo-card post-container">
            <div class="holo-border"></div>
            <main class="post-content">
                <article>
                    <h1>${articleTitle}</h1>
                    <p class="post-meta">Published on ${formattedPublishDate} by ${authorName}</p>
                    
                    ${articleContent}
                    
                    <div style="margin-top: 2rem; text-align: left;">
                        <a href="${pathPrefix}index.html" class="holo-button" style="display: inline-flex;">
                            <i class="fas fa-home"></i> Back Home
                        </a>
                    </div>
                </article>
            </main>
        </section>

    </div>

    <div class="particles" id="particles"></div>

    <footer>
        <div class="footer-links"> 
            <a href="${pathPrefix}index.html">Home</a> | 
            <a href="${pathPrefix}about.html">About</a> | 
            <a href="${pathPrefix}articles.html">Articles</a> | 
            <a href="${pathPrefix}privacy-policy.html">Privacy Policy</a> | 
            <a href="mailto:${contactEmail}">Contact</a>
        </div>
        <p class="copyright">&copy; ${new Date().getFullYear()} Logos Mind. All rights reserved.</p>
    </footer>

    <script src="${pathPrefix}script.js"></script>
</body>
</html>`;
        generatedHtmlOutput.value = articleHtml;

        // --- Generate JSON for all-articles.json ---
        const jsonForAll = {
            title: articleTitle,
            excerpt: metaDescription.substring(0, 100) + (metaDescription.length > 100 ? '...' : ''), // Simple excerpt
            link: rootRelativeArticlePath,
            imageUrl: rootRelativeImageUrl,
            tags: tagsArray,
            metaDescription: metaDescription, // Full meta description for search suggestion if needed
            datePublished: isoPublishDate // Added datePublished
        };
        generatedJsonAllOutput.value = JSON.stringify(jsonForAll, null, 2) + ',\n// Add this to the BEGINNING of the array in media/all-articles.json';

        // --- Generate JSON for index-recent.json (if checked) ---
        if (addToRecent) {
            const jsonForRecent = {
                title: articleTitle,
                excerpt: metaDescription.substring(0, 100) + (metaDescription.length > 100 ? '...' : ''),
                link: rootRelativeArticlePath,
                imageUrl: rootRelativeImageUrl,
                tags: tagsArray,
                metaDescription: metaDescription,
                datePublished: isoPublishDate // Added datePublished
            };
            generatedJsonRecentOutput.value = JSON.stringify(jsonForRecent, null, 2);
        } else {
            generatedJsonRecentOutput.value = ""; // Clear if not checked
        }
    });

    clearFormButton.addEventListener('click', () => {
        articleForm.reset();
        allArticlesJsonInput.value = ''; // Also clear sitemap input
        generatedHtmlOutput.value = '';
        generatedJsonAllOutput.value = '';
        generatedJsonRecentOutput.value = '';
        generatedSitemapXmlOutput.value = ''; // Also clear sitemap output
        document.getElementById('articleTitle').focus();
    });

    // --- Sitemap Generator Logic ---
    generateSitemapButton.addEventListener('click', () => {
        const articlesJsonString = allArticlesJsonInput.value;
        let articles = [];
        try {
            articles = JSON.parse(articlesJsonString);
            if (!Array.isArray(articles)) {
                alert("Invalid JSON format. Please paste the JSON array from media/all-articles.json.");
                return;
            }
        } catch (error) {
            alert("Error parsing JSON: " + error.message + "\nPlease ensure you pasted the correct content from media/all-articles.json.");
            return;
        }

        const today = new Date().toISOString().split('T')[0];
        let sitemapXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
`;

        // Helper function to create URL entries
        const createUrlEntry = (loc, lastmod = today, changefreq = 'weekly', priority = '0.8') => {
            // Ensure URL is absolute and correct
            let absoluteLoc = loc;
            if (!loc.startsWith(websiteBaseUrl)) {
                 absoluteLoc = `${websiteBaseUrl}/${loc}`.replace(/(?<!:)\/\//g, '/'); // Avoid double slashes
            }
            return `  <url>\n    <loc>${absoluteLoc}</loc>\n    <lastmod>${lastmod}</lastmod>\n    <changefreq>${changefreq}</changefreq>\n    <priority>${priority}</priority>\n  </url>\n`;
        };

        // Add static pages
        const staticPages = [
            { loc: '', priority: '1.0', changefreq: 'daily' }, // Homepage
            { loc: 'index.html', priority: '1.0', changefreq: 'daily' }, // Homepage alias
            { loc: 'about.html', priority: '0.7', changefreq: 'monthly' },
            { loc: 'articles.html', priority: '0.9', changefreq: 'daily' },
            { loc: 'privacy-policy.html', priority: '0.5', changefreq: 'yearly' }
        ];

        staticPages.forEach(page => {
            sitemapXml += createUrlEntry(page.loc, today, page.changefreq, page.priority);
        });

        // Add articles from JSON
        articles.forEach(article => {
            if (article.link) {
                const lastModDate = article.datePublished || today; // Use article.datePublished if available
                sitemapXml += createUrlEntry(article.link, lastModDate, 'monthly', '0.7'); 
            }
        });

        sitemapXml += '</urlset>';
        generatedSitemapXmlOutput.value = sitemapXml;
    });

    // --- Toolbar Logic ---
    if (toolbar && articleContentTextarea) {
        toolbar.addEventListener('click', (event) => {
            const target = event.target;
            if (target.classList.contains('toolbar-button')) {
                const tag = target.dataset.tag;
                wrapText(tag);
            }
        });
    }

    function wrapText(tag) {
        const textarea = articleContentTextarea;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);
        let replacement = '';
        
        // Get current pathPrefix based on articleFolder input for relative image paths
        const currentArticleFolder = document.getElementById('articleFolder').value.trim();
        let currentPathPrefix = './';
        if (currentArticleFolder) {
            const folderParts = currentArticleFolder.replace(/^\/+|\/+$/g, '').split('/').filter(part => part !== '');
            const depth = folderParts.length;
            if (depth > 0) {
                currentPathPrefix = '../'.repeat(depth);
            }
        }

        switch (tag) {
            case 'h1':
            case 'h2':
            case 'p':
            case 'strong':
            case 'em':
                replacement = `<${tag}>${selectedText}</${tag}>`;
                break;
            case 'blockquote':
                replacement = `<blockquote>\n  <p>${selectedText}</p>\n</blockquote>`;
                break;
            case 'ul':
            case 'ol':
                const lines = selectedText.split('\n').map(line => line.trim()).filter(line => line);
                if (lines.length > 0) {
                    replacement = `<${tag}>\n${lines.map(line => `  <li>${line}</li>`).join('\n')}\n</${tag}>`;
                } else {
                    replacement = `<${tag}>\n  <li>Item 1</li>\n  <li>Item 2</li>\n</${tag}>`;
                }
                break;
            case 'a':
                const url = prompt("Enter the URL:", "https://");
                if (url) {
                    replacement = `<a href="${url}">${selectedText || 'Link Text'}</a>`;
                }
                break;
            case 'img':
                const subfolder = prompt("Enter subfolder within /images/ (e.g., april-2025) or leave blank if none:");
                const srcFile = prompt("Enter image filename (e.g., image.jpg):");
                if (srcFile) {
                    const alt = prompt("Enter image alt text:", selectedText || "Image description");
                    let imagePath = "images/";
                    if (subfolder && subfolder.trim() !== "") {
                        imagePath += subfolder.trim() + "/";
                    }
                    imagePath += srcFile.trim();
                    replacement = `<img src="${currentPathPrefix}${imagePath}" alt="${alt || ''}">`; // Use calculated prefix
                }
                break;
            case 'figure':
                const figSubfolder = prompt("Enter subfolder within /images/ (e.g., april-2025) or leave blank if none:");
                const figSrcFile = prompt("Enter image filename (e.g., image.jpg):");
                if (figSrcFile) {
                    const figAlt = prompt("Enter image alt text:", "Image description");
                    const figCaption = prompt("Enter image caption:", selectedText || "Image caption");
                    let figImagePath = "images/";
                    if (figSubfolder && figSubfolder.trim() !== "") {
                        figImagePath += figSubfolder.trim() + "/";
                    }
                    figImagePath += figSrcFile.trim();
                    replacement = `<figure>\n  <img src="${currentPathPrefix}${figImagePath}" alt="${figAlt || ''}">\n  <figcaption>${figCaption || ''}</figcaption>\n</figure>`;
                }
                break;
            default:
                return;
        }

        if (replacement) {
            textarea.setRangeText(replacement, start, end, 'select');
            textarea.focus();
        }
    }

    // --- Character Counter Logic ---
    if (articleTitleInput && titleCharCountSpan) {
        articleTitleInput.addEventListener('input', () => {
            titleCharCountSpan.textContent = articleTitleInput.value.length;
        });
    }
    if (metaDescriptionInput && metaDescCharCountSpan) {
        metaDescriptionInput.addEventListener('input', () => {
            metaDescCharCountSpan.textContent = metaDescriptionInput.value.length;
        });
    }

    // --- Automatic Filename/Slug Generation Logic ---
    function generateFilename() {
        const title = articleTitleInput.value;
        const dateValue = publishDateInput.value;

        if (!title || !dateValue) {
            // Don't generate if title or date is missing
            // articleFilenameInput.value = ''; // Optional: clear filename if inputs are empty
            return; 
        }

        // Format date as DD-MM-YYYY
        const dateParts = dateValue.split('-'); // YYYY-MM-DD
        const formattedDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;

        // Generate slug from title
        const slug = title
            .toLowerCase()
            .trim()
            .replace(/\s+/g, '-')           // Replace spaces with -
            .replace(/[^Ѐ-ӿ\w\-]+/g, '') // Remove all non-word chars except cyrillic and hyphens
            .replace(/\-\-+/g, '-')         // Replace multiple - with single -
            .replace(/^-+/, '')             // Trim - from start of text
            .replace(/-+$/, '');            // Trim - from end of text

        if (slug) {
            articleFilenameInput.value = `${slug}-${formattedDate}.html`;
        } else {
            // Handle cases where the title results in an empty slug (e.g., only symbols)
            articleFilenameInput.value = `article-${formattedDate}.html`; 
        }
    }

    if (articleTitleInput && publishDateInput && articleFilenameInput) {
        articleTitleInput.addEventListener('input', generateFilename);
        publishDateInput.addEventListener('change', generateFilename); // Use 'change' for date input
    }

    // --- Copy to Clipboard Logic ---
    document.querySelectorAll('.copy-button').forEach(button => {
        button.addEventListener('click', () => {
            const targetId = button.dataset.target;
            const textarea = document.getElementById(targetId);
            if (textarea && textarea.value) {
                navigator.clipboard.writeText(textarea.value)
                    .then(() => {
                        const originalText = button.textContent;
                        button.textContent = 'Copied!';
                        setTimeout(() => {
                            button.textContent = originalText;
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Failed to copy: ', err);
                        alert('Failed to copy text. See console for details.');
                    });
            } else if (textarea) {
                const originalText = button.textContent;
                button.textContent = 'Nothing to copy!';
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            } 
        });
    });
});
