/* ===== DARK THEME OVERRIDES ===== */

/* Define dark theme color variables */
body.dark-theme {
    --primary: #1a1a1a;
    --secondary: #2c2c2c;
    --text: #e0e0e0;
    --text-light: #a0a0a0;
    
    /* Dark Theme Accent Colors */
    --holo-blue-dark: #3b82f6;
    --holo-green-dark: #10b981;

    /* Particle Colors (Dark Theme) */
    --particle-color-1: rgba(59, 130, 246, 0.4);
    --particle-color-2: rgba(16, 185, 129, 0.4);

    background-color: var(--primary);
    color: var(--text); 
}

/* Override specific component backgrounds/colors */

body.dark-theme .light-waves {
    background: linear-gradient(135deg, 
        rgba(26, 26, 26, 0.9) 0%, 
        rgba(44, 44, 44, 0.7) 50%, 
        rgba(26, 26, 26, 0.9) 100%);
    background-size: 400% 400%;
}

body.dark-theme .holo-card {
    background: rgba(44, 44, 44, 0.8);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Featured Card */
body.dark-theme .holo-card.featured {
    background-image: url('../images/featured-background.jpg');
    background-size: cover;
    background-position: center;
    background-color: transparent;
}

body.dark-theme .holo-card.featured .content {
     color: white;
     text-shadow: 0 1px 3px rgba(0,0,0,0.6);
}

body.dark-theme .holo-card.featured .content p {
    color: white;
    text-shadow: none;
}

body.dark-theme .holo-card.featured .featured-overlay {
    background: rgba(44, 44, 44, 0.85);
}

body.dark-theme .holo-card.featured:hover .featured-overlay {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    mask-image: radial-gradient(
        circle 250px at var(--mouse-x) var(--mouse-y),
        transparent 0%,
        transparent 40%,
        black 90%
    );
    -webkit-mask-image: radial-gradient(
        circle 250px at var(--mouse-x) var(--mouse-y),
        transparent 0%,
        transparent 40%,
        black 90%
    );
}

/* Other Cards & Panels */
body.dark-theme .post-card {
    background: var(--secondary);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

body.dark-theme .scripture-panel {
    background: linear-gradient(135deg, rgba(44, 44, 44, 0.9), rgba(60, 60, 60, 0.8));
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

body.dark-theme .scripture-panel h3 {
     color: var(--holo-green-dark);
}

/* Search Input */
body.dark-theme .search-container input[type="search"] {
    background-color: rgba(44, 44, 44, 0.7);
    border-color: var(--text-light);
    color: var(--text);
}
body.dark-theme .search-container input[type="search"]::placeholder {
    color: var(--text-light);
}

/* Search Suggestions */
body.dark-theme .suggestions-dropdown {
    background-color: var(--secondary);
    border-color: var(--text-light);
}

body.dark-theme .suggestions-dropdown a {
    color: var(--text);
}

body.dark-theme .suggestions-dropdown a:hover {
    background-color: var(--primary);
}

/* Pagination */
body.dark-theme .pagination-controls button,
body.dark-theme .pagination-controls span {
    background-color: var(--secondary);
    border-color: var(--text-light);
    color: #ccc;
}

body.dark-theme .pagination-controls button:hover {
    background-color: var(--holo-blue-dark);
    border-color: var(--holo-blue-dark);
    color: white;
}

body.dark-theme .pagination-controls .current-page {
    background-color: var(--holo-green-dark);
    border-color: var(--holo-green-dark);
    color: white;
}

/* Footer */
body.dark-theme .footer-links a {
    color: var(--text-light);
}

body.dark-theme .footer-links a:hover {
    color: var(--holo-blue-dark);
}

/* Gradient Element Overrides */
body.dark-theme .holo-gradient {
    background: linear-gradient(90deg, var(--holo-blue-dark), var(--holo-green-dark));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    animation: holoShift 8s ease infinite;
    background-size: 200% 200%;
}

body.dark-theme .holo-button {
    background: linear-gradient(90deg, var(--holo-blue-dark), var(--holo-green-dark));
    color: white;
}

body.dark-theme .holo-border { 
    background: linear-gradient(90deg, var(--holo-blue-dark), var(--holo-green-dark)) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
}

body.dark-theme .holo-badge {
    background: linear-gradient(90deg, var(--holo-blue-dark), var(--holo-green-dark));
    color: white;
}

body.dark-theme .search-container button {
    background: linear-gradient(90deg, var(--holo-blue-dark), var(--holo-green-dark));
    color: white;
}

/* Post Tag Overrides */
body.dark-theme .post-tag {
    background-color: #495057;
    color: var(--text);
}

/* Ensure specific buttons have white text in dark theme (overriding light theme rule) */
body.dark-theme .holo-card.featured .content a.holo-button,
body.dark-theme .view-all-container a.holo-button {
    color: white;
}