
function createParticles() {
    const particlesContainer = document.getElementById('particles');
    if (!particlesContainer) return;
    
    const totalParticleCount = 50;
    const numLayers = 3;
    const particlesPerLayer = totalParticleCount / numLayers;

    for (let layerIndex = 0; layerIndex < numLayers; layerIndex++) {
        for (let i = 0; i < particlesPerLayer; i++) {
            const particle = document.createElement('i');
            particle.classList.add('particle', 'fas', 'fa-book-bible');


            const layerMultiplier = layerIndex / (numLayers -1);

            particle.style.zIndex = numLayers - layerIndex;

            const baseSize = 10;
            const sizeVariance = 10;
            const size = (baseSize + Math.random() * sizeVariance) * (1 - layerMultiplier * 0.5);
        particle.style.fontSize = `${size}px`;
        
            const baseOpacity = 0.1;
            const opacityVariance = 0.3;
            const opacity = (baseOpacity + Math.random() * opacityVariance) * (1 - layerMultiplier * 0.6);
            particle.style.opacity = opacity;
            
            const baseDuration = 10;
            const durationVariance = 15;
            const duration = (baseDuration + Math.random() * durationVariance) * (1 + layerMultiplier * 1);
            particle.style.animationDuration = `${duration}s`;


            particle.style.color = Math.random() < 0.5 ? 'var(--particle-color-1)' : 'var(--particle-color-2)';

            particle.style.left = `${Math.random() * 100}vw`;
            particle.style.top = '105vh';
            
            particle.style.animationDelay = `${Math.random() * duration}s`;
        
        particlesContainer.appendChild(particle);
            allParticleElements.push(particle);
        }
    }
}


async function loadPosts() {
    const grid1 = document.getElementById('postGrid');
    const grid2 = document.getElementById('postGrid2');
    
    if (!grid1 || !grid2) {
        return;
    }

    try {
        const response = await fetch('media/index-recent.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const posts = await response.json();

        const createCardHTML = (post) => {
            let tagsHTML = '';
            if (post.tags && Array.isArray(post.tags) && post.tags.length > 0) {
                tagsHTML = `
                    <div class="post-tags-container">
                        ${post.tags.slice(0, 2).map(tag => `<span class="post-tag">${tag}</span>`).join('')}
                    </div>
                `;
            }
            return `
                <img src="${post.imageUrl}" alt="${post.title}" class="post-card-img">
                <div class="post-card-content">
                    <h3>${post.title}</h3>
                    <p>${post.excerpt}</p>
                    ${tagsHTML}
                    <a href="${post.link}">Read more <i class="fas fa-arrow-right"></i></a>
                </div>
            `;
        };

        grid1.innerHTML = '';
        grid2.innerHTML = '';

        posts.slice(0, 3).forEach(post => {
            const card = document.createElement('article');
            card.className = 'post-card';
            card.innerHTML = createCardHTML(post);
            grid1.appendChild(card);
        });

        posts.slice(3, 9).forEach(post => {
        const card = document.createElement('article');
        card.className = 'post-card';
            card.innerHTML = createCardHTML(post);
            grid2.appendChild(card);
        });

    } catch (error) {
        console.error('Error loading or parsing posts:', error);
        grid1.innerHTML = '<p>Error loading posts.</p>';
        grid2.innerHTML = '';
    }
}


let allPostsData = [];
let currentPage = 1;
const postsPerPage = 30;

let globalSearchInput = null;
let globalSearchButton = null;
let globalSuggestionsDropdown = null;
let searchDebounceTimer = null;
let themeToggleButton = null;
let sortFilterDropdown = null;
let allParticleElements = [];
let mouseX = -1000;
let mouseY = -1000;
let animationFrameRequest = null;


async function initializeSite() {
    try {
        if (allPostsData.length === 0) {
            const response = await fetch('media/all-articles.json'); 
            if (!response.ok) {
                throw new Error(`HTTP error fetching all articles: ${response.status}`);
            }
            allPostsData = await response.json();
            console.log("All articles data loaded for search.");
        }
    } catch (error) {
        console.error('Failed to load all articles data:', error);

    }

    // Get global elements if they exist on the page
    globalSearchInput = document.getElementById('globalSearchInput');
    globalSearchButton = document.getElementById('globalSearchButton');
    globalSuggestionsDropdown = document.getElementById('globalSuggestionsDropdown');
    themeToggleButton = document.getElementById('themeToggle');
    sortFilterDropdown = document.getElementById('sortFilterDropdown');


    if (themeToggleButton) {
        setupThemeToggle();
    } else {
        applyInitialTheme(); 
    }


    if (globalSearchInput && globalSearchButton && globalSuggestionsDropdown) {
        setupGlobalSearchListeners();
    }
    if (document.querySelector('.holo-card.featured')) {
        setupFeaturedCardHover();
    } 


    if (sortFilterDropdown) {
        sortFilterDropdown.addEventListener('change', () => {
            loadAllArticles(1);
        });
    }

    createParticles();
    loadPosts(); 
    

    let initialArticlesPage = 1;
    if (document.getElementById('allArticlesGrid')) {
        const urlParams = new URLSearchParams(window.location.search);
        const pageParam = parseInt(urlParams.get('page'), 10);
        if (!isNaN(pageParam) && pageParam > 0) {
            initialArticlesPage = pageParam;
        }
    }
    loadAllArticles(initialArticlesPage); 
    loadScripture(); 


    document.addEventListener('mousemove', handleParticleMouseMove);
    requestParticleInteractionFrame();
}


function setupThemeToggle() {
    const themeIcon = themeToggleButton.querySelector('i');
    if (!themeIcon) {
        console.error("Theme toggle button icon not found!");
        applyInitialTheme();
        return;
    }


    const applyTheme = (theme) => {
        if (theme === 'dark') {
            document.body.classList.add('dark-theme');
            themeIcon.classList.remove('fa-sun');
            themeIcon.classList.add('fa-moon');
        } else {
            document.body.classList.remove('dark-theme');
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
        }
    };


    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        applyTheme(savedTheme);
    } else {
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        applyTheme(prefersDark ? 'dark' : 'light');
    }


    themeToggleButton.addEventListener('click', () => {
        const currentTheme = document.body.classList.contains('dark-theme') ? 'dark' : 'light';
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        applyTheme(newTheme);
        localStorage.setItem('theme', newTheme);
    });
}

function applyInitialTheme() {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
         document.body.classList.add('dark-theme');
    } else if (!savedTheme) {
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        if(prefersDark) document.body.classList.add('dark-theme');
    }
}


function setupFeaturedCardHover() {
    const featuredCard = document.querySelector('.holo-card.featured');
    if (!featuredCard) return;

    featuredCard.addEventListener('mousemove', (e) => {
        const rect = featuredCard.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        featuredCard.style.setProperty('--mouse-x', `${x}px`);
        featuredCard.style.setProperty('--mouse-y', `${y}px`);
    });


    featuredCard.addEventListener('mouseleave', () => {
    });
}


function setupGlobalSearchListeners() {
    globalSearchButton.addEventListener('click', () => {
        if (document.getElementById('allArticlesGrid')) {
            filterArticles();
        } else {
            globalSuggestionsDropdown.style.display = 'none';
        }
    });
    

    globalSearchInput.addEventListener('input', handleGlobalSearchInput);
    globalSearchInput.addEventListener('search', handleGlobalSearchInput);


    document.addEventListener('click', (event) => {

        if (globalSearchInput && globalSuggestionsDropdown && 
            !globalSearchInput.contains(event.target) && 
            !globalSuggestionsDropdown.contains(event.target)) {
            globalSuggestionsDropdown.style.display = 'none';
        }
    });


    globalSearchInput.addEventListener('focus', () => {
        if (globalSearchInput.value.trim() !== '') {
            showGlobalSuggestions();
        }
    });
}

function handleGlobalSearchInput() {
    clearTimeout(searchDebounceTimer);
    searchDebounceTimer = setTimeout(() => {
        if (!globalSearchInput) return;
        if (globalSearchInput.value.trim() === '') {
            if (globalSuggestionsDropdown) globalSuggestionsDropdown.style.display = 'none';

            if (document.getElementById('allArticlesGrid')) {
                loadAllArticles(1);
            }
        } else {
            showGlobalSuggestions();
        }
    }, 250); 
}

function showGlobalSuggestions() {
    if (!globalSearchInput || !globalSuggestionsDropdown || allPostsData.length === 0) return;

    const searchTerm = globalSearchInput.value.trim().toLowerCase();
    if (searchTerm === '') {
        globalSuggestionsDropdown.style.display = 'none';
        return;
    }


    const filteredPosts = allPostsData.filter(post => {
        const title = post.title ? post.title.toLowerCase() : '';
        const excerpt = post.excerpt ? post.excerpt.toLowerCase() : '';
        return title.includes(searchTerm) || excerpt.includes(searchTerm);
    }).slice(0, 10);

    globalSuggestionsDropdown.innerHTML = '';

    if (filteredPosts.length > 0) {
        filteredPosts.forEach(post => {
            const link = document.createElement('a');
            link.href = post.link;
            

            if (window.location.pathname.includes('/april-2025/')) {
                 link.href = '../' + post.link;
            }
            

            const title = post.title || 'Untitled';
            const description = post.metaDescription || '';
            link.innerHTML = `${title}<br><small style="color: var(--text-light);">${description}</small>`;

            link.title = post.excerpt || '';
            globalSuggestionsDropdown.appendChild(link);
        });
        globalSuggestionsDropdown.style.display = 'block';
    } else {
        globalSuggestionsDropdown.style.display = 'none';
    }
}




async function loadAllArticles(page = 1) {
    const grid = document.getElementById('allArticlesGrid');
    const paginationContainer = document.getElementById('paginationControls');

    if (!grid || !paginationContainer) {
        return;
    }

    currentPage = page;


    if (allPostsData.length === 0) {
        grid.innerHTML = '<p>Article data not loaded yet. Please wait or refresh.</p>';
        console.error('loadAllArticles called before allPostsData was populated.');
        return;
    }


    if (globalSearchInput && globalSearchInput.value.trim() !== '') {
        filterArticles(); 
        return; 
    }

    const sortValue = sortFilterDropdown ? sortFilterDropdown.value : 'newest';
    let sortedData = [...allPostsData];

    if (sortValue === 'oldest') {
        sortedData.reverse();
    }



    const startIndex = (currentPage - 1) * postsPerPage;
    const endIndex = startIndex + postsPerPage;
    const postsToShow = sortedData.slice(startIndex, endIndex);

    const createCardHTML = (post) => {
        let tagsHTML = '';
        if (post.tags && Array.isArray(post.tags) && post.tags.length > 0) {
            tagsHTML = `
                <div class="post-tags-container">
                    ${post.tags.slice(0, 2).map(tag => `<span class="post-tag">${tag}</span>`).join('')}
                </div>
            `;
        }
        return `
            <img src="${post.imageUrl}" alt="${post.title}" class="post-card-img">
            <div class="post-card-content">
            <h3>${post.title}</h3>
            <p>${post.excerpt}</p>
                ${tagsHTML}
            <a href="${post.link}">Read more <i class="fas fa-arrow-right"></i></a>
            </div>
        `;
    };

    grid.innerHTML = '';
    
    postsToShow.forEach(post => {
        const card = document.createElement('article');
        card.className = 'post-card';
        card.innerHTML = createCardHTML(post);
        grid.appendChild(card);
    });

    if (globalSuggestionsDropdown) globalSuggestionsDropdown.style.display = 'none'; 
    paginationContainer.style.display = 'block';
    setupPagination(sortedData.length, paginationContainer);

    const currentUrl = `articles.html?page=${currentPage}`;
    history.replaceState({ page: currentPage }, '', currentUrl);
}


function filterArticles() {
    const grid = document.getElementById('allArticlesGrid');
    const paginationContainer = document.getElementById('paginationControls');
    if (!grid || !paginationContainer || !globalSearchInput) {
        return;
    }
    if (globalSuggestionsDropdown) globalSuggestionsDropdown.style.display = 'none';

    const searchTerm = globalSearchInput.value.trim().toLowerCase();

    if (searchTerm === '') {
        loadAllArticles(1);
        return;
    }


    const filteredPosts = allPostsData.filter(post => {
        const title = post.title ? post.title.toLowerCase() : '';
        const excerpt = post.excerpt ? post.excerpt.toLowerCase() : '';
        return title.includes(searchTerm) || excerpt.includes(searchTerm);
    });

    grid.innerHTML = '';
    paginationContainer.style.display = 'none';
    if (filteredPosts.length === 0) {
        grid.innerHTML = '<p style="text-align: center; width: 100%; grid-column: 1 / -1;">No articles found matching your search.</p>';
    } else {
        const createCardHTML = (post) => {
            let tagsHTML = '';
            if (post.tags && Array.isArray(post.tags) && post.tags.length > 0) {
                tagsHTML = `
                    <div class="post-tags-container">
                        ${post.tags.slice(0, 2).map(tag => `<span class="post-tag">${tag}</span>`).join('')}
                    </div>
                `;
            }
            return `
                <img src="${post.imageUrl}" alt="${post.title}" class="post-card-img">
                <div class="post-card-content">
            <h3>${post.title}</h3>
            <p>${post.excerpt}</p>
                ${tagsHTML}
            <a href="${post.link}">Read more <i class="fas fa-arrow-right"></i></a>
                </div>
            `;
        };
        filteredPosts.forEach(post => {
            const card = document.createElement('article');
            card.className = 'post-card';
            card.innerHTML = createCardHTML(post);
            grid.appendChild(card);
        });
    }
}


function setupPagination(totalItems, wrapper) {
    wrapper.innerHTML = '';
    const totalPages = Math.ceil(totalItems / postsPerPage);

    if (totalPages <= 1) return;

    const prevButton = document.createElement('button');
    prevButton.innerHTML = '&laquo; Prev';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => {
        if (currentPage > 1) {
            const newPage = currentPage - 1;
            const newUrl = `articles.html?page=${newPage}`;
            history.pushState({ page: newPage }, '', newUrl);
            loadAllArticles(newPage);
            window.scrollTo(0, 0);
        }
    });
    wrapper.appendChild(prevButton);

    const pageInfo = document.createElement('span');
    pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
    pageInfo.style.cursor = 'default';
    pageInfo.style.fontWeight = 'normal';
    pageInfo.style.backgroundColor = 'transparent';
    pageInfo.style.border = 'none';
    wrapper.appendChild(pageInfo);

    const nextButton = document.createElement('button');
    nextButton.innerHTML = 'Next &raquo;';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => {
        if (currentPage < totalPages) {
            const newPage = currentPage + 1;
            const newUrl = `articles.html?page=${newPage}`;
            history.pushState({ page: newPage }, '', newUrl);
            loadAllArticles(newPage);
            window.scrollTo(0, 0);
        }
    });
    wrapper.appendChild(nextButton);
}


async function loadScripture() { 
    const verseElement = document.querySelector('#dailyVerse');
    const citeElement = document.querySelector('.scripture-panel cite'); 
    if (!verseElement || !citeElement) {
        return; 
    }

    try {
        const response = await fetch('media/verse-of-the-day.json');
        if (!response.ok) {
            throw new Error(`HTTP error fetching verses: ${response.status}`);
        }
        const verses = await response.json();

        if (!verses || verses.length === 0) {
            console.warn("Verse data is empty or invalid from JSON.");
            verseElement.textContent = "Verse unavailable.";
            citeElement.textContent = "";
            return;
        }


        const now = new Date();
        const startOfYear = Date.UTC(now.getFullYear(), 0, 1);
        const todayUTC = Date.UTC(now.getFullYear(), now.getMonth(), now.getDate());
        const daysSinceYearStart = Math.floor((todayUTC - startOfYear) / (1000 * 60 * 60 * 24));

        const verseIndex = daysSinceYearStart % verses.length;
        const dailyVerse = verses[verseIndex];


        if (dailyVerse) {
             verseElement.textContent = dailyVerse.text;
             citeElement.textContent = `— ${dailyVerse.reference}`;
        } else {
             verseElement.textContent = "Verse not found for today.";
             citeElement.textContent = "";
        }

    } catch (error) {
        console.error("Failed to load daily scripture:", error);
        verseElement.textContent = "Could not load verse.";
        citeElement.textContent = "";
    }
}


function handleParticleMouseMove(event) {
    mouseX = event.clientX;
    mouseY = event.clientY;
}

function requestParticleInteractionFrame() {
    if (!animationFrameRequest) {
        animationFrameRequest = requestAnimationFrame(updateParticleInteraction);
    }
}

function updateParticleInteraction() {
    const interactionRadius = 100;
    const scaleFactor = 1.3;

    allParticleElements.forEach(particle => {
        const rect = particle.getBoundingClientRect();
        const particleCenterX = rect.left + rect.width / 2;
        const particleCenterY = rect.top + rect.height / 2;

        const distanceX = mouseX - particleCenterX;
        const distanceY = mouseY - particleCenterY;
        const distance = Math.sqrt(distanceX * distanceX + distanceY * distanceY);

        if (distance < interactionRadius) {
            particle.style.setProperty('--particle-scale', scaleFactor);
        } else {
            particle.style.setProperty('--particle-scale', 1);
        }
    });


    animationFrameRequest = null;
    requestParticleInteractionFrame(); 
}


document.addEventListener('DOMContentLoaded', initializeSite);