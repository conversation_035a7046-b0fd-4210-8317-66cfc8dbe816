:root {
    --primary: #f8f9fa;
    --secondary: #ffffff;
    --text: #3a3a3a;
    --text-light: #6c757d;
    --holo-blue: #7dd3fc;
    --holo-green: #4ade80;
    --font-main: 'Inter', sans-serif;
    /* Particle Colors (Light Theme) */
    --particle-color-1: rgba(125, 211, 252, 0.4);
    --particle-color-2: rgba(74, 222, 128, 0.4);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-main);
    color: var(--text);
    line-height: 1.6;
    font-size: 18px;
    background-color: var(--primary);
    overflow-x: hidden;
    overflow-y: scroll;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
}

.light-waves {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
        rgba(248, 249, 250, 0.9) 0%, 
        rgba(220, 231, 255, 0.7) 50%, 
        rgba(248, 249, 250, 0.9) 100%);
    background-size: 400% 400%;
    z-index: 1;
}

@keyframes gradientWave {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.holo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 0;
    position: relative;
    gap: 1rem;
}

.holo-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    flex-shrink: 0;
}

.holo-gradient {
    background: linear-gradient(90deg, var(--holo-blue), var(--holo-green));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    animation: holoShift 8s ease infinite;
    background-size: 200% 200%;
}

@keyframes holoShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.holo-header nav {
    display: flex;
    gap: 1rem;
    order: 1;
}

.holo-header .holo-button {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
}

.holo-header nav a {
    color: var(--text);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    padding: 0.5rem 0;
}

.holo-header nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--holo-blue), var(--holo-green));
    transition: width 0.3s ease;
}

.holo-header nav a:hover::after {
    width: 100%;
}

/* Theme Toggle Button Styles */
.theme-toggle-button {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 1.4rem;
    cursor: pointer;
    padding: 0.5rem;
    margin: 0 0.5rem;
    order: 2;
    line-height: 1;
}

.theme-toggle-button:hover {
    color: var(--holo-blue);
}

/* Adjust Search Container Order */
.search-container {
    position: relative;
    display: flex;
    flex-grow: 1;
    max-width: 500px;
    margin: 0 1rem;
    order: 3;
}

.holo-card {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16px;
    padding: 2rem;
    margin: 3rem 0;
    position: relative;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    overflow: hidden;
}

/* Specific styling for the FEATURED card */
.holo-card.featured {
    background-image: url('../images/featured-background.jpg'); 
    background-size: cover;
    background-position: center;
    padding: 0;
    color: white; 
    --mouse-x: 50%;
    --mouse-y: 50%;
}

/* The overlay that hides the image initially */
.featured-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px); 
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px;
    z-index: 1;
    pointer-events: none;
    transition: mask-image 0.1s ease-out, -webkit-mask-image 0.1s ease-out;
    mask-image: none;
    -webkit-mask-image: none;
}

/* Apply the mask on hover of the CARD */
.holo-card.featured:hover .featured-overlay {
    mask-image: radial-gradient(
        circle 250px at var(--mouse-x) var(--mouse-y),
        transparent 0%,
        transparent 40%,
        black 90%
    );
    -webkit-mask-image: radial-gradient(
        circle 250px at var(--mouse-x) var(--mouse-y), 
        transparent 0%, 
        transparent 40%, 
        black 90%
    );
}

.holo-border { 
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid transparent;
    border-radius: 16px;
    background: linear-gradient(90deg, var(--holo-blue), var(--holo-green)) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
    animation: borderGlow 6s linear infinite;
    z-index: 3;
}

/* Ensure content is above the overlay */
.holo-card .content {
    position: relative;
    z-index: 2;
    color: var(--text); 
}

/* Adjust featured card content color specifically if needed */
.holo-card.featured .content {
    position: relative;
    z-index: 2;
    padding: 2rem; 
    color: white;
    text-shadow: 0 1px 3px rgba(0,0,0,0.4);
}

/* Styles for text within the FEATURED card's content */
.holo-card.featured .content h2,
.holo-card.featured .content p {
    transition: opacity 0.3s ease-in-out;
}

/* Hide text on FEATURED card hover */
.holo-card.featured:hover .content h2,
.holo-card.featured:hover .content p {
    opacity: 0;
    pointer-events: none; 
}

.holo-badge {
    background: linear-gradient(90deg, var(--holo-blue), var(--holo-green));
    color: var(--text);
    padding: 0.3rem 1rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
}

.holo-card h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text);
}

.holo-card p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.holo-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: linear-gradient(90deg, var(--holo-blue), var(--holo-green));
    color: black;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.holo-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(125, 211, 252, 0.3);
}

/* Specific Button Text Colors (Light Theme - Index Page) */
.holo-card.featured .content a.holo-button,
.view-all-container a.holo-button {
    color: #333; /* Override default white text for these specific buttons */
}

.recent-posts {
    margin: 4rem 0;
}

.recent-posts h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
}

.holo-accent {
    background: linear-gradient(90deg, var(--holo-blue), var(--holo-green));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.post-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}

.post-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 480px;
}

.post-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 150px;
    background: linear-gradient(to bottom, var(--holo-blue), var(--holo-green));
}

.post-card-img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 12px 12px 0 0;
}

.post-card-content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(125, 211, 252, 0.2);
}

.post-card h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: var(--text);
}

.post-card p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.post-card a {
    color: var(--holo-blue);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.9rem;
    margin-top: auto;
    align-self: flex-start;
}

.post-card a:hover {
    color: var(--holo-green);
}

.scripture-panel {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(220, 231, 255, 0.8));
    border-radius: 16px;
    padding: 3rem 2rem;
    margin: 4rem 0;
    text-align: center;
    position: relative;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.holo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        linear-gradient(rgba(125, 211, 252, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(125, 211, 252, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    opacity: 0.5;
}

.scripture-panel h3 {
    color: var(--holo-green);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.scripture-panel blockquote {
    font-size: 1.8rem;
    font-style: italic;
    margin-bottom: 1rem;
    color: var(--text);
    line-height: 1.4;
}

.scripture-panel cite {
    color: var(--text-light);
    font-style: normal;
}

.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    text-shadow: 0 0 8px rgba(125, 211, 252, 0.3);
    animation: float 15s linear infinite;
    --particle-scale: 1;
}

@keyframes float {
    0% {
        transform: translateY(0) translateX(0) rotate(0deg) scale(var(--particle-scale));
        opacity: 0.4;
    }
    50% {
        transform: translateY(-50vh) translateX(50px) rotate(180deg) scale(var(--particle-scale));
    }
    90% {
        opacity: 0.4;
    }
    100% {
        transform: translateY(-100vh) translateX(-50px) rotate(360deg) scale(var(--particle-scale));
        opacity: 0;
    }
}

.post-meta {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

.post-content h2 {
    font-size: 1.5rem;
    margin: 2rem 0 1rem;
    color: var(--text);
}

.post-content p {
    text-align: justify;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
    hyphens: auto;
    margin-bottom: 1em;
}

.post-content ul {
    margin: 1rem 0 1.5rem 1.5rem;
    color: var(--text-light);
}

.post-content li {
    margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
    .holo-header {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .holo-header h1 {
        order: 1;
    }
    .search-container {
        order: 2;
        width: 100%;
        max-width: none;
        margin: 0;
    }
    
    .holo-header nav {
        order: 3;
        gap: 0.5rem;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .theme-toggle-button {
        order: 4;
        margin-top: 0.5rem;
    }
    
    .post-grid {
        grid-template-columns: 1fr;
    }
    
    .holo-card, .scripture-panel {
        padding: 1.5rem;
    }
    
    .holo-card h2, .recent-posts h2 {
        font-size: 1.5rem;
    }
    
    .scripture-panel blockquote {
        font-size: 1.4rem;
    }

    /* Mobile Image Scaling */
    .post-card-img {
        height: 200px; /* Adjust height for mobile */
    }

    .article-hero-image {
        height: auto; /* Allow height to scale proportionally */
    }

    .holo-card.featured {
        min-height: 300px; /* Ensure featured card has minimum height */
        background-position: center center; /* Ensure background image is centered */
    }
}

footer {
    text-align: center;
    padding: 2rem 0;
    margin-top: 4rem;
    color: var(--text-light);
    font-size: 0.9rem;
    position: relative; 
    z-index: 2;
}

/* Added styles for footer links */
.footer-links {
    margin-bottom: 1rem;
}

.footer-links a {
    color: var(--text-light);
    text-decoration: none;
    margin: 0 0.5rem;
}

.footer-links a:hover {
    color: var(--holo-blue);
    text-decoration: underline;
}

.article-hero-image {
    display: block; 
    width: 1200px;    /* Fixed width */
    max-width: 100%;  /* Responsive fallback */
    height: 500px;    /* Fixed height */
    margin-bottom: 2rem; 
    margin-left: auto;  /* Centering */
    margin-right: auto; /* Centering */
    border-radius: 8px; 
    object-fit: cover; 
}

.pagination-controls {
    text-align: center;
    margin-top: 3rem;
    margin-bottom: 2rem;
}

.pagination-controls button,
.pagination-controls span {
    margin: 0 0.3rem;
    padding: 0.5rem 1rem;
    border: 1px solid var(--text-light);
    border-radius: 4px;
    background-color: var(--secondary);
    color: var(--text);
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s;
}

.pagination-controls button:hover {
    background-color: var(--holo-blue);
    border-color: var(--holo-blue);
    color: white;
}

.pagination-controls button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.pagination-controls .current-page {
    font-weight: bold;
    background-color: var(--holo-green);
    border-color: var(--holo-green);
    color: white;
    cursor: default;
}

/* Added styles for View All button container */
.view-all-container {
    text-align: center;
    margin-top: 2.5rem;
}

.search-container input[type="search"] {
    flex-grow: 1;
    padding: 0.6rem 1rem;
    border: 1px solid var(--text-light);
    border-radius: 50px 0 0 50px;
    border-right: none;
    font-size: 0.9rem;
    background-color: rgba(255, 255, 255, 0.7);
    color: var(--text);
}

.search-container input[type="search"]::placeholder {
    color: var(--text-light);
}

.search-container button {
    padding: 0.6rem 1rem;
    border: 1px solid var(--text-light);
    border-radius: 0 50px 50px 0;
    background: linear-gradient(90deg, var(--holo-blue), var(--holo-green));
    color: white;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Suggestions Dropdown Styles */
.suggestions-dropdown {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--secondary);
    border: 1px solid var(--text-light);
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 10;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.suggestions-dropdown a {
    display: block;
    padding: 0.8rem 1rem;
    color: var(--text);
    text-decoration: none;
    font-size: 0.9rem;
    line-height: 1.4;
}

.suggestions-dropdown a small {
    display: block;
    margin-top: 0.2em;
}

.suggestions-dropdown a:hover {
    background-color: var(--primary);
}

/* Style adjustments for dark theme */
body.dark-theme .suggestions-dropdown {
    background-color: var(--secondary-dark);
    border-color: var(--text-light-dark);
}

body.dark-theme .suggestions-dropdown a:hover {
    background-color: var(--primary-dark);
}

/* Filter Dropdown Styling */
.filter-container select {
    padding: 0.5rem 0.8rem;
    border: 1px solid var(--text-light);
    border-radius: 4px;
    background-color: var(--secondary);
    color: var(--text);
    font-size: 0.9rem;
    cursor: pointer;
}

/* Dark theme adjustment for select */
body.dark-theme .filter-container select {
    background-color: var(--secondary);
    border-color: var(--text-light);
    color: var(--text);
}

/* Post Card Tag Styles */
.post-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
    margin-bottom: 0.8rem;
}

.post-tag {
    background-color: #e9ecef;
    color: var(--text-light);
    padding: 0.2rem 0.6rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
}